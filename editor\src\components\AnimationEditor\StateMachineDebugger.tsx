/**
 * 状态机调试器组件
 * 用于调试和监控状态机的运行状态
 */
import React, { useState, useEffect, useRef } from 'react';
import { Table, Button, Space, Tabs, Timeline, Card, Descriptions, Tag, Tooltip, Empty, Spin } from 'antd';
import { 
  ReloadOutlined, 
  ClearOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined,
  BugOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { stateMachineService } from '../../services/stateMachineService';
import { DebugEvent, DebugEventType } from '../../../../engine/src/animation/AnimationStateMachineDebugger';
// 移除引擎直接导入
import './StateMachineEditor.less';

const { TabPane } = Tabs;

/**
 * 状态机调试器属性
 */
interface StateMachineDebuggerProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 状态机调试器组件
 */
const StateMachineDebugger: React.FC<StateMachineDebuggerProps> = ({ entityId }) => {
  const { t } = useTranslation();
  
  // 状态
  const [events, setEvents] = useState<DebugEvent[]>([]);
  const [parameters, setParameters] = useState<any[]>([]);
  const [currentState, setCurrentState] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('events');
  
  // 引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 加载调试信息
  useEffect(() => {
    loadDebugInfo();
    
    // 设置定时器，定期更新调试信息
    timerRef.current = setInterval(loadDebugInfo, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [entityId]);
  
  // 加载调试信息
  const loadDebugInfo = async () => {
    try {
      setLoading(true);
      const info = await stateMachineService.getStateMachineDebugInfo(entityId);
      
      if (info) {
        setEvents(info.events || []);
        setParameters(info.parameters || []);
        setCurrentState(info.currentState || null);
      }
    } catch (error) {
      console.error('加载调试信息失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 清空事件
  const handleClearEvents = async () => {
    try {
      await stateMachineService.clearStateMachineDebugEvents(entityId);
      setEvents([]);
    } catch (error) {
      console.error('清空调试事件失败:', error);
    }
  };
  
  // 刷新调试信息
  const handleRefresh = () => {
    loadDebugInfo();
  };
  
  // 获取事件类型标签
  const getEventTypeTag = (type: DebugEventType) => {
    switch (type) {
      case DebugEventType.STATE_ENTER:
        return <Tag color="green">{t('editor.animation.stateEnter')}</Tag>;
      case DebugEventType.STATE_EXIT:
        return <Tag color="red">{t('editor.animation.stateExit')}</Tag>;
      case DebugEventType.TRANSITION_START:
        return <Tag color="blue">{t('editor.animation.transitionStart')}</Tag>;
      case DebugEventType.TRANSITION_END:
        return <Tag color="purple">{t('editor.animation.transitionEnd')}</Tag>;
      case DebugEventType.CONDITION_EVALUATE:
        return <Tag color="orange">{t('editor.animation.conditionEvaluate')}</Tag>;
      case DebugEventType.PARAMETER_CHANGE:
        return <Tag color="cyan">{t('editor.animation.parameterChange')}</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };
  
  // 渲染事件数据
  const renderEventData = (event: DebugEvent) => {
    const { type, data } = event;
    
    switch (type) {
      case DebugEventType.STATE_ENTER:
      case DebugEventType.STATE_EXIT:
        return (
          <div>
            <strong>{t('editor.animation.state')}:</strong> {data.state.name}
          </div>
        );
      case DebugEventType.TRANSITION_START:
      case DebugEventType.TRANSITION_END:
        return (
          <div>
            <strong>{t('editor.animation.from')}:</strong> {data.transition.from},&nbsp;
            <strong>{t('editor.animation.to')}:</strong> {data.transition.to}
          </div>
        );
      case DebugEventType.CONDITION_EVALUATE:
        return (
          <div>
            <strong>{t('editor.animation.condition')}:</strong> {data.expression},&nbsp;
            <strong>{t('editor.animation.result')}:</strong> {data.result ? 'true' : 'false'}
          </div>
        );
      case DebugEventType.PARAMETER_CHANGE:
        return (
          <div>
            <strong>{t('editor.animation.parameter')}:</strong> {data.name},&nbsp;
            <strong>{t('editor.animation.oldValue')}:</strong> {JSON.stringify(data.oldValue)},&nbsp;
            <strong>{t('editor.animation.newValue')}:</strong> {JSON.stringify(data.newValue)}
          </div>
        );
      default:
        return <pre>{JSON.stringify(data, null, 2)}</pre>;
    }
  };
  
  // 渲染事件列表
  const renderEvents = () => {
    if (events.length === 0) {
      return (
        <Empty description={t('editor.animation.noEvents')} />
      );
    }
    
    const columns = [
      {
        title: t('editor.animation.time'),
        dataIndex: 'time',
        key: 'time',
        render: (time: number) => time.toFixed(2) + 's'
      },
      {
        title: t('editor.animation.type'),
        dataIndex: 'type',
        key: 'type',
        render: (type: DebugEventType) => getEventTypeTag(type)
      },
      {
        title: t('editor.animation.data'),
        dataIndex: 'data',
        key: 'data',
        render: (_data: any, record: DebugEvent) => renderEventData(record)
      }
    ];
    
    return (
      <Table
        dataSource={events}
        columns={columns}
        rowKey={(record) => `${record.type}-${record.time}`}
        pagination={{ pageSize: 10 }}
        size="small"
      />
    );
  };
  
  // 渲染事件时间线
  const renderTimeline = () => {
    if (events.length === 0) {
      return (
        <Empty description={t('editor.animation.noEvents')} />
      );
    }
    
    return (
      <Timeline mode="left">
        {events.map((event, index) => (
          <Timeline.Item
            key={index}
            label={event.time.toFixed(2) + 's'}
            color={
              event.type === DebugEventType.STATE_ENTER ? 'green' :
              event.type === DebugEventType.STATE_EXIT ? 'red' :
              event.type === DebugEventType.TRANSITION_START ? 'blue' :
              event.type === DebugEventType.TRANSITION_END ? 'purple' :
              event.type === DebugEventType.CONDITION_EVALUATE ? 'orange' :
              event.type === DebugEventType.PARAMETER_CHANGE ? 'cyan' :
              'gray'
            }
          >
            <div>
              {getEventTypeTag(event.type)}
              {renderEventData(event)}
            </div>
          </Timeline.Item>
        ))}
      </Timeline>
    );
  };
  
  // 渲染参数列表
  const renderParameters = () => {
    if (parameters.length === 0) {
      return (
        <Empty description={t('editor.animation.noParameters')} />
      );
    }
    
    const columns = [
      {
        title: t('editor.animation.parameterName'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('editor.animation.parameterType'),
        dataIndex: 'type',
        key: 'type'
      },
      {
        title: t('editor.animation.parameterValue'),
        dataIndex: 'value',
        key: 'value',
        render: (value: any) => JSON.stringify(value)
      }
    ];
    
    return (
      <Table
        dataSource={parameters}
        columns={columns}
        rowKey="name"
        pagination={false}
        size="small"
      />
    );
  };
  
  // 渲染当前状态
  const renderCurrentState = () => {
    if (!currentState) {
      return (
        <Empty description={t('editor.animation.noCurrentState')} />
      );
    }
    
    return (
      <Card title={t('editor.animation.currentState')}>
        <Descriptions bordered size="small">
          <Descriptions.Item label={t('editor.animation.stateName')} span={3}>
            {currentState.name}
          </Descriptions.Item>
          <Descriptions.Item label={t('editor.animation.stateType')} span={3}>
            {currentState.type}
          </Descriptions.Item>
          {currentState.type === 'SingleAnimationState' && (
            <>
              <Descriptions.Item label={t('editor.animation.clipName')} span={3}>
                {currentState.clipName}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.animation.loop')} span={3}>
                {currentState.loop ? t('editor.yes') : t('editor.no')}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.animation.clamp')} span={3}>
                {currentState.clamp ? t('editor.yes') : t('editor.no')}
              </Descriptions.Item>
            </>
          )}
          {currentState.type === 'BlendAnimationState' && (
            <>
              <Descriptions.Item label={t('editor.animation.parameterName')} span={3}>
                {currentState.parameterName}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.animation.blendSpaceType')} span={3}>
                {currentState.blendSpaceType}
              </Descriptions.Item>
            </>
          )}
        </Descriptions>
      </Card>
    );
  };
  
  return (
    <div className="state-machine-debugger">
      <div className="debugger-header">
        <h3>
          <BugOutlined /> {t('editor.animation.debugger')}
        </h3>
        
        <Space>
          <Tooltip title={t('editor.animation.refresh')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              size="small"
            />
          </Tooltip>
          <Tooltip title={t('editor.animation.clearEvents')}>
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearEvents}
              size="small"
            />
          </Tooltip>
        </Space>
      </div>
      
      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('editor.animation.events')} key="events">
            {renderEvents()}
          </TabPane>
          <TabPane tab={t('editor.animation.timeline')} key="timeline">
            {renderTimeline()}
          </TabPane>
          <TabPane tab={t('editor.animation.parameters')} key="parameters">
            {renderParameters()}
          </TabPane>
          <TabPane tab={t('editor.animation.currentState')} key="currentState">
            {renderCurrentState()}
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default StateMachineDebugger;
